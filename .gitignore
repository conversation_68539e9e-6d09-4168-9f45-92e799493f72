# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Environment variables
local.env
prod.env
.env
src/purchases/examples
src/common/utils/retry.util.md
src/common/utils/retry.util.spec.ts
src/purchases/test-email-retry.ts
src/purchases/test-email.ts
src/purchases/test-multilingual-email.ts
src/mail/templates/activation/README.md
src/purchases/SHOPIFY_INTECRATION.md
src/purchases/test-sms-offline.ts
src/purchases/test-sms.ts
src/sms/README.md
