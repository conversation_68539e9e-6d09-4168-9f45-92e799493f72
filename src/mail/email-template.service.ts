import { Injectable, Logger } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import { ShopifyOrder } from '../purchases/interfaces/shopify.interface';

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
  inlinedAttachments?: Array<{
    ContentID: string;
    ContentType: string;
    Base64Content: string;
    Filename: string;
  }>;
}

interface TemplateVariables {
  customerName: string;
  activationCode: string;
  orderNumber: string;
  currency: string;
  totalPrice: string;
  email: string;
}

@Injectable()
export class EmailTemplateService {
  private readonly logger = new Logger(EmailTemplateService.name);
  private readonly templatesPath = join(
    process.cwd(),
    'src',
    'mail',
    'templates',
    'activation',
  );
  private readonly sharedStylesPath = join(
    process.cwd(),
    'src',
    'mail',
    'templates',
    'shared-styles.css',
  );
  private readonly logoPath = join(
    process.cwd(),
    'src',
    'mail',
    'templates',
    'activation',
    'imvi-logo.png',
  );
  private translationsConfig: any;
  private sharedStyles: string;
  private logoBase64: string;

  constructor() {
    this.logger.debug(`[Constructor] Templates path: ${this.templatesPath}`);

    // Load shared styles
    try {
      this.sharedStyles = readFileSync(this.sharedStylesPath, 'utf8');
      this.logger.debug(
        `[Constructor] Loaded shared styles successfully (${this.sharedStyles.length} characters)`,
      );

      if (this.sharedStyles.length < 100) {
        this.logger.warn(
          '[Constructor] Shared styles seem too short, this might cause issues',
        );
      }
    } catch (error) {
      this.logger.error('Failed to load shared styles:', error);
      this.sharedStyles = '';
    }

    // Load logo PNG and convert to base64
    try {
      const logoBuffer = readFileSync(this.logoPath);
      this.logoBase64 = logoBuffer.toString('base64');
      this.logger.debug(
        `[Constructor] Loaded logo PNG successfully (${this.logoBase64.length} base64 characters)`,
      );
    } catch (error) {
      this.logger.error('Failed to load logo PNG:', error);
      this.logoBase64 = '';
    }

    const translationsPath = join(this.templatesPath, 'translations.json');
    this.logger.debug(
      `[Constructor] Translations config path: ${translationsPath}`,
    );

    try {
      this.translationsConfig = JSON.parse(
        readFileSync(translationsPath, 'utf8'),
      );

      this.logger.debug(
        `[Constructor] Loaded translations config successfully`,
      );
      this.logger.debug(
        `[Constructor] Supported languages: ${this.translationsConfig.supportedLanguages}`,
      );
    } catch (error) {
      this.logger.error('Failed to load translations config:', error);
      this.logger.log('Using default translations from file as fallback');

      try {
        // Try to require the translations.json file directly as fallback
        const defaultTranslations = require('./templates/activation/translations.json');
        this.translationsConfig = defaultTranslations;
        this.logger.log('Successfully loaded fallback translations from file');
      } catch (fallbackError) {
        this.logger.error(
          'Failed to load fallback translations:',
          fallbackError,
        );
        throw new Error('Unable to load translations from any source');
      }
    }
  }

  /**
   * Detect language based on billing address country
   */
  detectLanguageFromOrder(shopifyOrder: ShopifyOrder): string {
    const country =
      shopifyOrder.billing_address?.country ||
      shopifyOrder.shipping_address?.country;

    if (!country) {
      this.logger.warn('No country found in order, using default language');
      return this.translationsConfig.defaultLanguage;
    }

    this.logger.debug(`[Language Detection] Raw country value: "${country}"`);

    // Try exact match first
    let language = this.translationsConfig.countryToLanguage[country];
    this.logger.debug(
      `[Language Detection] Exact match for "${country}": ${
        language || 'none'
      }`,
    );

    // If no exact match, try case-insensitive match
    if (!language) {
      const countryUpper = country.toUpperCase();
      language = this.translationsConfig.countryToLanguage[countryUpper];
      this.logger.debug(
        `[Language Detection] Case-insensitive match for "${countryUpper}": ${
          language || 'none'
        }`,
      );
    }

    // The countryToLanguage mapping in translations.json should handle all cases
    // including both country codes and country names

    const detectedLanguage =
      language || this.translationsConfig.defaultLanguage;

    this.logger.log(
      `Detected language '${detectedLanguage}' for country '${country}'`,
    );

    // Log available country mappings for debugging
    if (!language) {
      this.logger.warn(
        `[Language Detection] No mapping found for country "${country}". Available mappings:`,
        Object.keys(this.translationsConfig.countryToLanguage),
      );
    }

    return detectedLanguage;
  }

  /**
   * Get activation email template for specific language
   */
  getActivationEmailTemplate(
    language: string,
    variables: TemplateVariables,
  ): EmailTemplate {
    this.logger.debug(`[Template Loading] Requested language: ${language}`);
    this.logger.debug(
      `[Template Loading] Templates path: ${this.templatesPath}`,
    );

    // Ensure language is supported
    if (!this.translationsConfig.supportedLanguages.includes(language)) {
      this.logger.warn(
        `Language '${language}' not supported, falling back to default`,
      );
      language = this.translationsConfig.defaultLanguage;
    }

    try {
      // Load single HTML template
      const htmlPath = join(this.templatesPath, 'template.html');
      this.logger.debug(`[Template Loading] HTML template path: ${htmlPath}`);
      let htmlTemplate = readFileSync(htmlPath, 'utf8');

      // Load single text template
      const textPath = join(this.templatesPath, 'template.txt');
      this.logger.debug(`[Template Loading] Text template path: ${textPath}`);
      let textTemplate = readFileSync(textPath, 'utf8');

      // Get translations for the requested language
      const translations =
        this.translationsConfig.translations[language] ||
        this.translationsConfig.translations[
          this.translationsConfig.defaultLanguage
        ];

      // Combine template variables with translation variables
      const allVariables = {
        ...variables,
        ...translations,
      };

      // Inject shared styles, logo, and replace variables in templates
      htmlTemplate = this.injectSharedStyles(htmlTemplate);
      htmlTemplate = this.injectLogo(htmlTemplate);
      htmlTemplate = this.replaceVariables(htmlTemplate, allVariables);
      textTemplate = this.replaceVariables(textTemplate, allVariables);

      // Prepare inline attachments for logo
      const inlinedAttachments = this.logoBase64
        ? [
            {
              ContentID: 'IMVILogo',
              ContentType: 'image/png',
              Base64Content: this.logoBase64,
              Filename: 'imvi-logo.png',
            },
          ]
        : undefined;

      return {
        subject: translations.subject,
        html: htmlTemplate,
        text: textTemplate,
        inlinedAttachments,
      };
    } catch (error) {
      this.logger.error(
        `Failed to load template for language '${language}':`,
        error,
      );

      // Fallback to default language
      if (language !== this.translationsConfig.defaultLanguage) {
        this.logger.log(
          `Falling back to default language: ${this.translationsConfig.defaultLanguage}`,
        );
        return this.getActivationEmailTemplate(
          this.translationsConfig.defaultLanguage,
          variables,
        );
      }

      throw new Error(
        `Failed to load email template for language: ${language}`,
      );
    }
  }

  /**
   * Inject shared styles into HTML template
   */
  private injectSharedStyles(htmlTemplate: string): string {
    if (!this.sharedStyles) {
      return htmlTemplate;
    }

    // Replace existing <style> tags with shared styles
    const styleRegex = /<style[^>]*>[\s\S]*?<\/style>/gi;
    const sharedStylesTag = `<style>\n${this.sharedStyles}\n    </style>`;

    return htmlTemplate.replace(styleRegex, sharedStylesTag);
  }

  /**
   * Inject logo PNG image into HTML template
   */
  private injectLogo(htmlTemplate: string): string {
    if (!this.logoBase64) {
      return htmlTemplate;
    }

    // Replace the logo placeholder with an img tag referencing the inline attachment
    const logoPlaceholder = '<div class="logo">IMVI</div>';
    const logoImgTag =
      '<img src="cid:IMVILogo" alt="IMVI Logo" class="logo-img" width="80" height="80" />';

    return htmlTemplate.replace(logoPlaceholder, logoImgTag);
  }

  /**
   * Replace template variables with actual values
   */
  private replaceVariables(
    template: string,
    variables: TemplateVariables,
  ): string {
    let result = template;

    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), value || '');
    });

    return result;
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): string[] {
    return this.translationsConfig.supportedLanguages;
  }

  /**
   * Get default language
   */
  getDefaultLanguage(): string {
    return this.translationsConfig.defaultLanguage;
  }
}
