import { Injectable } from '@nestjs/common';
const Mailjet = require('node-mailjet');

@Injectable()
export class MailService {
  private mailjet: any;

  constructor() {
    const apiKey: string = process.env.MJ_KEY || '';
    const apiSecret: string = process.env.MJ_SECRET || '';
    this.mailjet = Mailjet.apiConnect(apiKey, apiSecret);
  }

  getMailJet(): any {
    return this.mailjet;
  }

  async sendMail(
    email: string,
    name: string,
    subject: string,
    text: string,
    html: string,
    inlinedAttachments?: Array<{
      ContentID: string;
      ContentType: string;
      Base64Content: string;
      Filename?: string;
    }>,
  ) {
    let message = {
      From: {
        Email: '<EMAIL>',
        Name: 'IMVI',
      },
      To: [
        {
          Email: email,
          Name: name,
        },
      ],
      Subject: subject,
      TextPart: text,
      HTMLPart: html
    };

    if (inlinedAttachments) {
      message['InlinedAttachments'] = inlinedAttachments;
    }
    const Messages = [message];

    // Temporary debug logging to diagnose HTML email issue
    console.log('[DEBUG] Email being sent to Mailjet:');
    console.log(`  HTML length: ${html?.length || 0}`);
    console.log(`  Text length: ${text?.length || 0}`);
    console.log(`  HTML has styles: ${html?.includes('font-family') ? 'YES' : 'NO'}`);
    console.log(`  HTML has content: ${html?.includes('<div') ? 'YES' : 'NO'}`);

    try {
      const request = await this.mailjet
        .post('send', { version: 'v3.1' })
        .request({
          Messages: Messages,
        });

      const result = await request;
      console.log(result.body);
      return result.body;
    } catch (error) {
      console.log('Error sending email:', error);
      throw new Error('Mail sending failed');
    }
  }
}
