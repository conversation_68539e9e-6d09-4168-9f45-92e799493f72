# Shopify Integration for Purchase Management

This document describes how the purchase system handles Shopify orders and webhooks.

## Overview

The system supports processing Shopify orders through a dedicated webhook endpoint:
- **Webhook Processing**: Using the `/shopify-webhook` endpoint for automatic processing of Shopify orders

## Features

- Automatic extraction of customer information from Shopify orders
- Product analysis to determine VR glasses and license quantities
- Address and billing information processing
- Order status tracking
- Duplicate order prevention
- Comprehensive logging and activity tracking

## Endpoints

### Shopify Webhook Handler

**Endpoint**: `POST /purchases/shopify-webhook`

**Description**: Processes incoming Shopify order webhooks automatically. This endpoint is designed to be called by Shopify when orders are created or updated.

**Example Shopify Webhook Payload**:
```json
{
  "id": 12345,
  "order_number": "1001",
  "name": "#1001",
  "email": "<EMAIL>",
  "financial_status": "paid",
  "fulfillment_status": "unfulfilled",
  "total_price": "99.99",
  "currency": "USD",
  "created_at": "2023-01-01T00:00:00Z",
  "customer": {
    "first_name": "<PERSON>",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "billing_address": {
    "address1": "123 Main St",
    "address2": "Apt 4B",
    "city": "New York",
    "province": "NY",
    "zip": "10001",
    "country": "USA",
    "phone": "+**********"
  },
  "shipping_address": {
    "address1": "123 Main St",
    "city": "New York",
    "province": "NY",
    "zip": "10001",
    "country": "USA"
  },
  "line_items": [
    {
      "title": "VR Training License",
      "quantity": 2,
      "price": "49.99",
      "sku": "VR-LICENSE-001",
      "product_id": "67890",
      "variant_id": "11111"
    }
  ]
}
```

## Product Detection Logic

The system analyzes line items to determine product types:

- **VR Glasses**: Items with titles/SKUs containing "vr", "glasses"
- **Licenses**: Items with titles/SKUs containing "license"
- **Subscriptions**: Items with titles/SKUs containing "subscription"
- **Duration**: 
  - Monthly products: 30 days
  - Yearly products: 365 days
  - Default: 365 days

## Code Generation

Purchase codes are generated as unique 4-character codes:
- Uses lowercase letters (a-z), uppercase letters (A-Z), and numbers (0-9)
- Ensures uniqueness by checking against existing purchase codes in database
- Automatically retries if duplicate is found (max 100 attempts)
- Examples: `aB3x`, `K9mP`, `7Qw2`

## Error Handling

- **Duplicate Orders**: Returns 409 Conflict if order already processed
- **Invalid Data**: Returns 400 Bad Request for malformed data
- **Processing Errors**: Returns 500 Internal Server Error with detailed logging

## Activity Logging

All Shopify order processing is logged with:
- Shopify order ID and number
- Financial and fulfillment status
- Order amount and currency
- Processing timestamps
- Error details (if any)

## Setup Instructions

1. **Configure Shopify Webhook**:
   - In Shopify Admin, go to Settings > Notifications
   - Add webhook URL: `https://your-domain.com/purchases/shopify-webhook`
   - Select "Order creation" and "Order updates" events
   - Set format to JSON

2. **Authentication**: The webhook endpoint is excluded from authentication middleware

3. **Testing**: Use the provided test cases or Shopify's webhook testing tools

## Security Considerations

- Webhook endpoint is publicly accessible (required for Shopify)
- Consider implementing webhook signature verification
- Monitor for suspicious activity through admin activity logs
- Implement rate limiting if needed

## Monitoring

- Check admin activity logs for webhook processing
- Monitor purchase creation patterns
- Set up alerts for failed webhook processing


# Email Template System

This directory contains the new unified email template system that uses a single template with translation variables instead of multiple language-specific templates.

## Files Structure

```
activation/
├── README.md           # This documentation
├── Logo.svg           # Original IMVI logo (SVG format)
├── imvi-logo.png      # IMVI logo (PNG format, used in email templates)
├── template.html      # Single HTML template with translation placeholders
├── template.txt       # Single text template with translation placeholders
└── translations.json  # All translations and configuration
```

## How It Works

### 1. Single Template Approach
Instead of maintaining separate HTML/text files for each language (e.g., `en.html`, `sv.html`, etc.), we now use:
- **One HTML template** (`template.html`) with translation placeholders
- **One text template** (`template.txt`) with translation placeholders
- **One translations file** (`translations.json`) containing all language-specific content
- **PNG logo** (`imvi-logo.png`) embedded as inline attachment for better email client compatibility

### 2. Translation Placeholders
Templates use double curly braces `{{placeholder}}` for both:
- **Dynamic variables**: `{{customerName}}`, `{{activationCode}}`
- **Translation variables**: `{{welcome}}`, `{{thankYou}}`, `{{step1}}`

### 3. Language Detection
The system automatically detects the appropriate language based on:
- Country code from Shopify order billing address
- Fallback to English for unsupported countries

### 4. Single Source of Truth
All configuration is centralized in `translations.json`:
- **Country-to-language mappings**: No hardcoded mappings in TypeScript code
- **All translations**: Every piece of text for every language
- **Supported languages list**: Centrally managed
- **Smart fallback**: Uses `require()` to load translations.json even if file reading fails
- **Emergency fallback**: Minimal hardcoded fallback only for absolute critical errors

## Supported Languages

- **English (en)**: Default language, used as fallback
- **Swedish (sv)**: Sweden
- **Finnish (fi)**: Finland  
- **Norwegian (no)**: Norway
- **German (de)**: Germany, Austria, Switzerland
- **Danish (da)**: Denmark

## Adding New Languages

To add a new language:

1. **Add language to supported list** in `translations.json`:
```json
"supportedLanguages": ["en", "sv", "fi", "no", "de", "da", "fr"]
```

2. **Add country mapping** in `translations.json`:
```json
"countryToLanguage": {
  "FR": "fr",
  "France": "fr"
}
```

3. **Add translations** in `translations.json`:
```json
"translations": {
  "fr": {
    "subject": "Votre code d'activation d'entraînement - IMVI",
    "title": "Votre code d'activation d'entraînement",
    "lang": "fr",
    "welcome": "Bienvenue à l'entraînement!",
    "greeting": "Cher/Chère",
    // ... add all required translation keys
  }
}
```

## Translation Keys

All translation keys used in templates:

- `subject`: Email subject line
- `title`: HTML page title
- `lang`: HTML language attribute
- `welcome`: Main heading
- `greeting`: Greeting text (Dear/Kära/etc.)
- `thankYou`: Thank you message
- `activationCodeTitle`: "Your Activation Code" heading
- `activationCodeDescription`: Description under activation code
- `nextStepsTitle`: "Next Steps" heading
- `step1`, `step2`, `step3`, `step4`: Individual step instructions
- `supportText`: Support contact message
- `regards`: Closing greeting
- `teamName`: Team signature
- `supportEmail`: Support email address
- `websiteText`: "Visit us at" text
- `websiteUrl`: Website URL
- `footerText`: Footer disclaimer text

## Benefits of New System

### ✅ Advantages
- **Single source of truth**: One template to maintain, all translations in one JSON file
- **Easier maintenance**: Changes apply to all languages
- **Consistent structure**: All languages have identical layout
- **Reduced duplication**: No repeated HTML/CSS across files, no hardcoded mappings
- **Easy translation updates**: All text in one JSON file
- **Type safety**: Centralized translation keys
- **No code duplication**: Country-to-language mapping exists only in translations.json

### 🔄 Migration
The old system had multiple sources of duplication:
- Separate template files: `en.html`, `sv.html`, `fi.html`, etc.
- Separate text files: `en.txt`, `sv.txt`, `fi.txt`, etc.
- Hardcoded country mappings in `EmailTemplateService.getCountryName()`
- Fallback country mappings in `EmailTemplateService` constructor

All of these have been replaced with the new unified system where:
- **One HTML template** with translation placeholders
- **One text template** with translation placeholders
- **One translations.json** as the single source of truth for all mappings and text

## Fallback Mechanism

The system has a robust fallback mechanism to prevent crashes:

1. **Primary**: Load `translations.json` using `readFileSync()`
2. **Fallback**: If file reading fails, use `require()` to load the same file
3. **Emergency**: If both fail, use minimal hardcoded English translations

This ensures the system always works, even in edge cases, while maintaining the single source of truth principle.

## Testing

Run the test to verify all languages work correctly:

```bash
npx ts-node src/purchases/test-multilingual-email.ts
```

Available test functions:
- `testSingleTemplateSystem()` - Test the new unified template system
- `testFallbackMechanism()` - Test the fallback loading mechanism
- `testMultilingualEmails()` - Test the original multilingual functionality
- `testLanguageDetection()` - Test language detection logic

## Usage Example

```typescript
import { EmailTemplateService } from '../mail/email-template.service';

const emailTemplateService = new EmailTemplateService();

const template = emailTemplateService.getActivationEmailTemplate('sv', {
  customerName: 'Erik Andersson',
  activationCode: 'AB3X',
  orderNumber: 'SE-1001',
  currency: 'SEK',
  totalPrice: '999.00',
  email: '<EMAIL>'
});

// template.subject: "Din träning aktiveringskod - IMVI"
// template.html: HTML with Swedish translations
// template.text: Text with Swedish translations
```
