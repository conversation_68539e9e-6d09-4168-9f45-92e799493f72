import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Res,
  Request,
  Query,
  HttpException,
  HttpStatus,
  Logger,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PurchasesService } from './purchases.service';
import { CreatePurchaseDto } from './dto/create-purchase.dto';
import { UpdatePurchaseDto } from './dto/update-purchase.dto';
import { ActivatePurchaseDto } from './dto/activate-purchase.dto';
import { LinkPurchaseActivationDto } from './dto/link-purchase-activation.dto';
import { PurchaseActivationDto } from './dto/purchase-activation.dto';
import { PurchaseAdditionalInfoDto } from './dto/purchase_additional_info.dto';
import { AuthGuard } from '../auth_admin/adminAuth.guard';
import { AdminActivitiesService } from '../admin_activities/adminActivities.service';
import { ShopifyOrder } from './interfaces/shopify.interface';
import { DatabaseService } from '../database/database.service';
import { verifySessionToken } from '../auth_admin/utils';
import { MailService } from '../mail/mail.service';
import { EmailTemplateService } from '../mail/email-template.service';
import { SmsService } from '../sms/sms.service';
import { SmsTemplateService } from '../sms/sms-template.service';
import { RetryUtil } from '../common/utils/retry.util';
import * as moment from 'moment';

@ApiTags('purchases')
@Controller('purchases')
export class PurchasesController {
  constructor(
    private readonly purchasesService: PurchasesService,
    private readonly adminActivitiesService: AdminActivitiesService,
    private readonly database: DatabaseService,
    private readonly mailService: MailService,
    private readonly emailTemplateService: EmailTemplateService,
    private readonly smsService: SmsService,
    private readonly smsTemplateService: SmsTemplateService,
  ) {}
  private readonly logger = new Logger(PurchasesController.name);

  /**
   * Helper method to extract and validate authentication token
   * Returns the authenticated user if valid token is provided, null otherwise
   */
  private async getAuthenticatedUser(request: any): Promise<any> {
    try {
      const token = this.extractTokenFromHeader(request);
      if (!token) {
        return null;
      }

      const sessions = await this.database.login_session.findMany({
        where: {
          expires: {
            gt: moment().toISOString(),
          },
        },
        include: {
          user: true,
        },
      });

      const session = sessions.find((s) => {
        return verifySessionToken(token, s.sessionToken);
      });

      if (!session) {
        return null;
      }

      return {
        id: session.user.id,
        email: session.user.email,
        role: session.user.role,
        sessionId: session.id,
      };
    } catch (error) {
      this.logger.warn('Token validation failed:', error.message);
      return null;
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  @Post('/linkPurchaseActivation')
  @ApiOperation({
    summary: 'Link a purchase to a user',
    description:
      'Links an existing purchase activation to a user based on the provided UUID and activation ID.',
  })
  @ApiResponse({
    status: 200,
    description: 'Purchase activation linked successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'User or purchase activation not found.',
  })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async linkPurchaseActivation(
    @Body() linkPurchaseActivationDto: LinkPurchaseActivationDto,
    @Res() res,
  ) {
    try {
      // Upsert user and get user details
      const user = await this.purchasesService.upsertUserByUuid(
        linkPurchaseActivationDto.uuid,
      );

      // Link user to purchase activation
      const purchaseActivation =
        await this.purchasesService.linkUserToPurchaseActivation(
          linkPurchaseActivationDto.activationId,
          user.id,
        );

      return res.json(purchaseActivation);
    } catch (error) {
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }

  @Post('/activate')
  @ApiOperation({
    summary: 'Activate a purchase',
    description:
      'Activates a purchase using the provided unique activation code.',
  })
  @ApiResponse({ status: 200, description: 'Purchase activated successfully.' })
  @ApiResponse({ status: 404, description: 'Activation code not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async activatePurchase(
    @Body() activatePurchaseDto: ActivatePurchaseDto,
    @Res() res,
  ) {
    try {
      const activation = await this.purchasesService.activatePurchase(
        activatePurchaseDto.code,
      );
      res.json(activation);
    } catch (error) {
      this.logger.error(
        `[PurchaseAPI] - Error activating purchase with code: ${activatePurchaseDto.code}: ${error.message}`,
        error.stack,
      );
      let status = HttpStatus.INTERNAL_SERVER_ERROR;
      if (error instanceof HttpException) {
        status = error.getStatus();
      }
      res.status(status).json({ error: error.response || error.message });
    }
  }

  @Post(`/:UserUUID/reactivateAccountWithPurchase`)
  @ApiOperation({
    summary: 'Reactivate an account with a purchase',
    description:
      'Reactivates an account using the provided unique activation code.',
  })
  @ApiParam({
    name: 'UserUUID',
    description: 'The unique identifier of the user to reactivate.',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account reactivated successfully.',
  })
  @ApiResponse({ status: 404, description: 'Activation code not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async reactivateAccountWithPurchase(
    @Param('UserUUID') userUUID: string,
    @Body() reactivatePurchaseDto: ActivatePurchaseDto,
    @Res() res,
  ) {
    try {
      const activation =
        await this.purchasesService.reactivateAccountWithPurchase(
          userUUID,
          reactivatePurchaseDto.code,
        );
      res.json(activation);
    } catch (error) {
      this.logger.error(
        `[PurchaseAPI] - Error reactivating account with code: ${reactivatePurchaseDto.code}: ${error.message}`,
        error.stack,
      );
      let status = HttpStatus.INTERNAL_SERVER_ERROR;
      if (error instanceof HttpException) {
        status = error.getStatus();
      }
      res.status(status).json({ error: error.response || error.message });
    }
  }

  @Post('/addPurchase')
  @ApiOperation({
    summary: 'Create a new purchase',
    description: 'Creates a new purchase entry with the provided details.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase created successfully.',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Purchase code already used or validation failed.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Purchase info validation failed.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error.',
  })
  async addPurchase(
    @Body() createPurchaseDto: CreatePurchaseDto,
    @Res() res,
    @Request() req,
  ) {
    try {
      // Check for authenticated admin user
      const authenticatedUser = await this.getAuthenticatedUser(req);

      const existingPurchase = await this.purchasesService.findPurchaseByCode(
        createPurchaseDto.code,
      );

      if (existingPurchase) {
        throw new HttpException(
          'Purchase code already used',
          HttpStatus.CONFLICT,
        );
      }

      const result = await this.purchasesService.addPurchase(createPurchaseDto);

      await this.adminActivitiesService.logActivity({
        admin_id: authenticatedUser?.id || null,
        action: 'CREATE_PURCHASE',
        entity: authenticatedUser?.id ? 'admin' : 'webshop',
        entity_id: result.id?.toString() || null,
        details: {
          purchase_code: createPurchaseDto.code,
          email: createPurchaseDto.email,
          order_number: createPurchaseDto.orderNumber,
        },
      });

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      const authenticatedUser = await this.getAuthenticatedUser(req);
      if (authenticatedUser?.id) {
        await this.adminActivitiesService.logActivity({
          admin_id: authenticatedUser.id,
          action: 'CREATE_PURCHASE_FAILED',
          entity: 'purchase',
          details: {
            error: error.message,
            purchase_code: createPurchaseDto.code,
          },
        });
      }

      if (error.status === HttpStatus.CONFLICT) {
        return res.status(HttpStatus.CONFLICT).json({ error: error.message });
      }
      return res.status(HttpStatus.BAD_REQUEST).json({ error: error.message });
    }
  }

  @Post('/createShopifyPurchase')
  @ApiOperation({
    summary: 'Handle Shopify order webhook',
    description:
      'Processes incoming Shopify order webhooks and creates purchase entries automatically. This endpoint is designed to be called by Shopify when orders are created or updated.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shopify order processed successfully.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid Shopify order data.',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Order already processed.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error.',
  })
  async handleShopifyWebhook(@Body() shopifyOrder: ShopifyOrder, @Res() res) {
    try {
      // Log the raw webhook data for debugging (truncated for security)
      const logData = JSON.stringify(shopifyOrder, null, 2);
      const truncatedLog =
        logData.length > 1000 ? logData.substring(0, 1000) + '...' : logData;
      this.logger.log(
        `[ShopifyWebhook] - Received webhook data: ${truncatedLog}`,
      );

      // Validate that we have the minimum required data
      if (!shopifyOrder || typeof shopifyOrder !== 'object') {
        this.logger.error(
          '[ShopifyWebhook] - Invalid webhook payload: not an object',
        );
        return res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Invalid webhook payload: not an object',
        });
      }

      this.logger.log(
        `[ShopifyWebhook] - Processing Shopify order: ${shopifyOrder.id}`,
      );

      // Check if order already exists - handle multiple possible order identifiers
      const possibleOrderNumbers = shopifyOrder.id?.toString();

      let existingPurchase = null;
      let matchedOrderNumber = '';

      for (const orderNum of possibleOrderNumbers) {
        if (orderNum) {
          existingPurchase =
            await this.purchasesService.findPurchaseByOrderNumber(
              String(orderNum),
            );
          if (existingPurchase) {
            matchedOrderNumber = String(orderNum);
            break;
          }
        }
      }

      if (existingPurchase) {
        this.logger.warn(
          `[ShopifyWebhook] - Order ${matchedOrderNumber} already processed`,
        );
        return res.status(HttpStatus.CONFLICT).json({
          error: 'Order already processed',
          purchase_id: existingPurchase.id,
          order_number: matchedOrderNumber,
        });
      }

      // Convert Shopify order to purchase DTO
      const createPurchaseDto =
        await this.purchasesService.convertShopifyOrderToPurchase(shopifyOrder);

      const result = await this.purchasesService.addPurchase(createPurchaseDto);

      // Send activation email to customer with retry logic
      try {
        await RetryUtil.withRetry(
          async () => {
            await this.sendActivationEmail(
              createPurchaseDto.email,
              `${createPurchaseDto.firstName} ${createPurchaseDto.lastName}`,
              result.code,
              shopifyOrder,
            );
          },
          {
            maxRetries: 3,
            baseDelay: 2000, // Start with 2 seconds
            maxDelay: 10000, // Max 10 seconds between retries
            retryCondition: (error) => this.isRetryableEmailError(error),
            onRetry: (error, attempt) => {
              this.logger.warn(
                `[ShopifyWebhook] - Email sending attempt ${attempt}/3 failed for ${createPurchaseDto.email}`,
                {
                  error: error.message,
                  errorCode: error.code,
                  errorName: error.name,
                  email: createPurchaseDto.email,
                  activationCode: result.code,
                  attempt,
                  isRetryable: this.isRetryableEmailError(error),
                },
              );
            },
          },
        );
        this.logger.log(
          `[ShopifyWebhook] - Activation email sent successfully`,
          {
            email: createPurchaseDto.email,
            customerName: `${createPurchaseDto.firstName} ${createPurchaseDto.lastName}`,
            activationCode: result.code,
            orderNumber: shopifyOrder.order_number || shopifyOrder.id,
          },
        );
      } catch (emailError) {
        this.logger.error(
          `[ShopifyWebhook] - Failed to send activation email after 3 attempts`,
          {
            email: createPurchaseDto.email,
            customerName: `${createPurchaseDto.firstName} ${createPurchaseDto.lastName}`,
            activationCode: result.code,
            orderNumber: shopifyOrder.order_number || shopifyOrder.id,
            error: emailError.message,
            errorCode: emailError.code,
            errorName: emailError.name,
            stack: emailError.stack,
          },
        );
        // Don't fail the entire process if email sending fails
        // The purchase has been created successfully, email failure is not critical
      }
      // Send activation SMS to customer if phone number is available
      try {
        await this.sendActivationSms(
          `${createPurchaseDto.firstName}`,
          result.code,
          shopifyOrder,
        );
      } catch (smsError) {
        this.logger.warn(
          `[ShopifyWebhook] - Failed to send activation SMS (non-critical)`,
          {
            email: createPurchaseDto.email,
            customerName: `${createPurchaseDto.firstName} ${createPurchaseDto.lastName}`,
            activationCode: result.code,
            orderNumber: shopifyOrder.name,
            error: smsError.message,
          },
        );
        // SMS failure is not critical, continue processing
      }

      await this.adminActivitiesService.logActivity({
        admin_id: null,
        action: 'SHOPIFY_WEBHOOK_PROCESSED',
        entity: 'shopify',
        entity_id: result.id?.toString() || null,
        details: {
          shopify_order_id: shopifyOrder.id,
          shopify_order_number: shopifyOrder.name,
          financial_status: shopifyOrder.financial_status,
          fulfillment_status: shopifyOrder.fulfillment_status,
          total_price: shopifyOrder.total_price,
          currency: shopifyOrder.currency,
          webhook_processed_at: new Date().toISOString(),
        },
      });

      this.logger.log(
        `[ShopifyWebhook] - Successfully processed Shopify order: ${
          shopifyOrder.id || shopifyOrder.order_number
        }`,
      );

      return res.status(HttpStatus.OK).json({
        message: 'Shopify order processed successfully',
        purchase_id: result.id,
        purchase_code: result.code,
      });
    } catch (error) {
      this.logger.error(
        `[ShopifyWebhook] - Error processing Shopify order: ${error.message}`,
        error.stack,
      );

      await this.adminActivitiesService.logActivity({
        admin_id: null,
        action: 'SHOPIFY_WEBHOOK_FAILED',
        entity: 'shopify',
        details: {
          error: error.message,
          shopify_order_id: shopifyOrder?.id,
          shopify_order_number: shopifyOrder?.name,
          webhook_failed_at: new Date().toISOString(),
        },
      });

      if (error.status === HttpStatus.CONFLICT) {
        return res.status(HttpStatus.CONFLICT).json({ error: error.message });
      }
      return res.status(HttpStatus.BAD_REQUEST).json({ error: error.message });
    }
  }

  @Get('/purchaseInfo/:id')
  @ApiOperation({
    summary: 'Get details on a specific purchase',
    description:
      'Retrieves detailed information for a specific purchase based on its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the purchase to retrieve.',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Purchase details retrieved successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'No purchase found with the provided ID.',
  })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async processPurchaseInfo(@Param('id') id: string, @Res() res) {
    try {
      const result = await this.purchasesService.getPurchaseInfo(id);
      if (result) {
        res.status(HttpStatus.OK).send(result);
      } else {
        res
          .status(HttpStatus.NOT_FOUND)
          .send(`No purchase found with id #${id}`);
      }
    } catch (error) {
      console.error('Error processing data:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/all-purchases')
  @ApiOperation({
    summary: 'Get all purchases',
    description: 'Retrieves all purchases information',
  })
  @ApiResponse({
    status: 200,
    description: 'Purchases details retrieved successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'Purchases not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  async getAllPurchases(
    @Res() res,
    @Query('page') page: number = 0,
    @Query('limit') limit: number = 100,
  ) {
    try {
      const limitNumber =
        isNaN(Number(limit)) || Number(limit) <= 0 ? 100 : Number(limit);
      const totalCount = await this.purchasesService.getPurchasesCount();
      const totalPages = Math.ceil(totalCount / limitNumber);
      const pageNumber =
        isNaN(Number(page)) || Number(page) <= 0 ? totalPages : Number(page);
      const result = await this.purchasesService.getPurchases(
        pageNumber,
        limitNumber,
      );
      if (result) {
        const data = {
          purchases: result,
          total: totalCount,
          currentPage: pageNumber,
          totalPages,
        };
        res.status(HttpStatus.OK).send(data);
      } else {
        res.status(HttpStatus.NOT_FOUND).send(`Purchases not found`);
      }
    } catch (error) {
      console.error('Error processing data:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/order-status/:orderId')
  @ApiOperation({
    summary: 'Get order status on a specific purchase',
    description:
      'Retrieves order status for a specific purchase based on corresponding order number.',
  })
  @ApiParam({
    name: 'orderId',
    description: 'The unique order id of the purchase to retrieve.',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Order status retrieved successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'No purchase found with the provided order ID.',
  })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async handleOrderStatus(@Param('orderId') orderId: string, @Res() res) {
    try {
      const result = await this.purchasesService.getOrderStatus(orderId);
      if (result) {
        res.status(HttpStatus.OK).send(result);
      } else {
        res
          .status(HttpStatus.NOT_FOUND)
          .send(`No order status found with order ID #${orderId}`);
      }
    } catch (error) {
      console.error('Error processing data:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/shipping-info/:id')
  @ApiOperation({
    summary: 'Get shipping info on a specific purchase',
    description:
      'Retrieves shipping info for a specific purchase based on its ID.',
  })
  @ApiParam({
    name: 'id',
    description:
      'The unique identifier of the purchase to get corresponding shipping info.',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Shipping info details retrieved successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'No shipping found with the provided ID.',
  })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async handleOrderShippingInfo(@Param('id') id: string, @Res() res) {
    try {
      const result = await this.purchasesService.getOrderShippingInfo(id);
      if (result) {
        res.status(HttpStatus.OK).send(result);
      } else {
        res
          .status(HttpStatus.NOT_FOUND)
          .send(`No shipping info found with id #${id}`);
      }
    } catch (error) {
      console.error('Error processing data:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/activations/:purchaseId')
  @ApiOperation({
    summary: 'Get activations for a specific purchase',
    description: 'Retrieves all activations associated with a purchase ID.',
  })
  @ApiParam({
    name: 'purchaseId',
    description:
      'The unique identifier of the purchase to get corresponding activations.',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Purchase activations retrieved successfully.',
    type: [PurchaseActivationDto],
  })
  @ApiResponse({
    status: 404,
    description: 'No activations found for the provided purchase ID.',
  })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async getPurchaseActivations(
    @Param('purchaseId') purchaseId: string,
    @Res() res,
  ) {
    const methodName = 'getPurchaseActivations';
    try {
      const numericId = parseInt(purchaseId, 10);

      if (isNaN(numericId)) {
        throw new HttpException(
          'Invalid purchase ID format',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result =
        await this.purchasesService.getPurchaseActivations(numericId);

      if (result && result.length > 0) {
        res.status(HttpStatus.OK).send(result);
      } else {
        res
          .status(HttpStatus.NOT_FOUND)
          .send(`No activations found for purchase ID #${purchaseId}`);
      }
    } catch (error) {
      this.logger.error(
        `[PurchasesController] ${methodName}: Error fetching activations for purchase ID ${purchaseId}: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Failed to fetch purchase activations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Update purchase information',
    description: 'Updates specific fields of a purchase record.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the purchase to update.',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Purchase updated successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data.',
  })
  @ApiResponse({
    status: 404,
    description: 'Purchase not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  async updatePurchase(
    @Param('id') id: string,
    @Body() updatePurchaseDto: UpdatePurchaseDto,
    @Res() res,
    @Request() req,
  ) {
    const methodName = 'updatePurchase';
    try {
      const numericId = parseInt(id, 10);

      if (isNaN(numericId)) {
        throw new HttpException(
          'Invalid purchase ID format',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result = await this.purchasesService.updatePurchase(
        numericId,
        updatePurchaseDto,
      );

      if (result) {
        await this.adminActivitiesService.logActivity({
          admin_id: req.user.id,
          action: 'UPDATE_PURCHASE',
          entity: 'purchase',
          entity_id: numericId.toString(),
          details: {
            updated_fields: updatePurchaseDto,
            purchase_id: numericId,
          },
        });

        this.logger.log(
          `${methodName}: Successfully updated purchase with ID: ${numericId}`,
        );
        res.status(HttpStatus.OK).send(result);
      } else {
        this.logger.warn(
          `${methodName}: No purchase found with ID: ${numericId}`,
        );
        res
          .status(HttpStatus.NOT_FOUND)
          .send(`No purchase found with ID #${id}`);
      }
    } catch (error) {
      if (req.user?.id) {
        await this.adminActivitiesService.logActivity({
          admin_id: req.user.id,
          action: 'UPDATE_PURCHASE_FAILED',
          entity: 'purchase',
          entity_id: id,
          details: {
            error: error.message,
            attempted_updates: updatePurchaseDto,
          },
        });
      }
      this.logger.error(
        `${methodName}: Error updating purchase with ID ${id}: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('additional-info/:purchaseId')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Create additional info for a specific purchase',
    description:
      'Creates a new additional info record for the specified purchase ID.',
  })
  @ApiParam({
    name: 'purchaseId',
    description: 'The unique identifier of the purchase',
    type: String,
  })
  @ApiResponse({
    status: 201,
    description: 'Additional info created successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid purchase ID or request body.',
  })
  @ApiResponse({
    status: 404,
    description: 'Purchase not found.',
  })
  async createAdditionalInfo(
    @Param('purchaseId') purchaseId: string,
    @Body() createDto: PurchaseAdditionalInfoDto,
    @Res() res,
    @Request() req,
  ) {
    const methodName = 'createAdditionalInfo';
    try {
      const numericId = parseInt(purchaseId, 10);
      const purchase = await this.purchasesService.findOne(numericId);
      if (!purchase) {
        return res
          .status(HttpStatus.NOT_FOUND)
          .json({ message: `Purchase with ID ${purchaseId} not found` });
      }

      const result = await this.purchasesService.createPurchaseAdditionalInfo(
        numericId,
        createDto,
      );

      await this.adminActivitiesService.logActivity({
        admin_id: req.user.id,
        action: 'CREATE_PURCHASE_ADDITIONAL_INFO',
        entity: 'purchase_additional_info',
        entity_id: result.id.toString(),
        details: {
          purchase_id: numericId,
          created_info: createDto,
        },
      });

      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      if (req.user?.id) {
        await this.adminActivitiesService.logActivity({
          admin_id: req.user.id,
          action: 'CREATE_PURCHASE_ADDITIONAL_INFO_FAILED',
          entity: 'purchase_additional_info',
          entity_id: purchaseId,
          details: {
            error: error.message,
            attempted_creation: createDto,
          },
        });
      }
      this.logger.error(
        `${methodName}: Error creating additional info for purchase ${purchaseId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to create additional info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('additional-info/:purchaseId')
  @ApiOperation({
    summary: 'Get additional info for a specific purchase',
    description:
      'Retrieves all additional info records for the specified purchase ID.',
  })
  @ApiParam({
    name: 'purchaseId',
    description: 'The unique identifier of the purchase',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Additional info retrieved successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid purchase ID.',
  })
  @ApiResponse({
    status: 404,
    description: 'Purchase not found or no additional info available.',
  })
  async getAdditionalInfo(@Param('purchaseId') purchaseId: string, @Res() res) {
    const methodName = 'getAdditionalInfo';
    try {
      const numericId = parseInt(purchaseId, 10);
      const purchase = await this.purchasesService.findOne(numericId);
      if (!purchase) {
        return res
          .status(HttpStatus.NOT_FOUND)
          .json({ message: `Purchase with ID ${purchaseId} not found` });
      }

      const additionalInfo =
        await this.purchasesService.findPurchaseAdditionalInfo(numericId);

      if (!additionalInfo.length) {
        return res.status(HttpStatus.NOT_FOUND).json({
          message: `No additional info found for purchase ID ${purchaseId}`,
        });
      }

      return res.status(HttpStatus.OK).json(additionalInfo);
    } catch (error) {
      this.logger.error(
        `${methodName}: Error fetching additional info for purchase ${purchaseId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to fetch additional info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch('additional-info/:id')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Update additional info for a specific purchase',
    description:
      'Updates an existing additional info record for the specified purchase ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the purchase',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Additional info updated successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid purchase ID, info ID, or request body.',
  })
  @ApiResponse({
    status: 404,
    description: 'Purchase or additional info record not found.',
  })
  async updateAdditionalInfo(
    @Param('id') id: string,
    @Body() updateDto: PurchaseAdditionalInfoDto,
    @Res() res,
    @Request() req,
  ) {
    const methodName = 'updateAdditionalInfo';
    try {
      const numericId = parseInt(id, 10);
      const purchase = await this.purchasesService.findOne(numericId);
      if (!purchase) {
        return res.status(HttpStatus.NOT_FOUND).json({
          message: `Purchase additional info with ID ${id} not found`,
        });
      }
      const existingInfo =
        await this.purchasesService.findPurchaseAdditionalInfoById(numericId);
      if (!existingInfo) {
        return res.status(HttpStatus.NOT_FOUND).json({
          message: `Additional info not found for id ${id}`,
        });
      }

      const result = await this.purchasesService.updatePurchaseAdditionalInfo(
        numericId,
        updateDto,
      );

      await this.adminActivitiesService.logActivity({
        admin_id: req.user.id,
        action: 'UPDATE_PURCHASE_ADDITIONAL_INFO',
        entity: 'purchase_additional_info',
        entity_id: id,
        details: {
          updated_fields: updateDto,
          purchase_id: numericId,
        },
      });

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      if (req.user?.id) {
        await this.adminActivitiesService.logActivity({
          admin_id: req.user.id,
          action: 'UPDATE_PURCHASE_ADDITIONAL_INFO_FAILED',
          entity: 'purchase_additional_info',
          entity_id: id,
          details: {
            error: error.message,
            attempted_updates: updateDto,
          },
        });
      }
      this.logger.error(
        `${methodName}: Error updating additional info for purchase additional info ${id}: ${error.message}`,
        error.stack,
      );

      if (error.code === 'P2025') {
        throw new HttpException(
          'Record to update not found.',
          HttpStatus.NOT_FOUND,
        );
      }

      throw new HttpException(
        'Failed to update additional info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('all-info-by-date-range')
  @ApiOperation({
    summary: 'Get purchase info and statuses by date range',
    description:
      'Retrieves all purchase info and statuses statuses within the specified date range',
  })
  async getPurchaseStatusesByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Res() res,
  ) {
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new HttpException('Invalid date format', HttpStatus.BAD_REQUEST);
      }

      const result = await this.purchasesService.getPurchaseStatusesByDateRange(
        start,
        end,
      );

      res.status(HttpStatus.OK).send(result);
    } catch (error) {
      this.logger.error(
        `Error fetching purchase info by date range: ${error.message}`,
        error.stack,
      );

      // More specific error handling
      if (error instanceof HttpException) {
        return res.status(error.getStatus()).json({ error: error.message });
      }

      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch purchase info',
        details: error.message,
      });
    }
  }

  /**
   * Send activation email to customer with their activation code
   */
  private async sendActivationEmail(
    email: string,
    customerName: string,
    activationCode: string,
    shopifyOrder: ShopifyOrder,
  ): Promise<void> {
    // Detect language from billing address country
    const language =
      this.emailTemplateService.detectLanguageFromOrder(shopifyOrder);

    // Additional debugging for language detection
    this.logger.debug(
      `[Email Debug] Order billing country: ${shopifyOrder.billing_address?.country}`,
    );
    this.logger.debug(
      `[Email Debug] Order shipping country: ${shopifyOrder.shipping_address?.country}`,
    );
    this.logger.debug(`[Email Debug] Detected language: ${language}`);

    // Prepare template variables
    const templateVariables = {
      customerName,
      activationCode,
      orderNumber:
        shopifyOrder.order_number || shopifyOrder.id?.toString() || '',
      currency: shopifyOrder.currency || 'USD',
      totalPrice: shopifyOrder.total_price || '0.00',
      email,
    };

    // Get localized email template
    const emailTemplate = this.emailTemplateService.getActivationEmailTemplate(
      language,
      templateVariables,
    );

    // Send the email using the localized template
    await this.mailService.sendMail(
      email,
      customerName,
      emailTemplate.subject,
      emailTemplate.text,
      emailTemplate.html,
      emailTemplate.inlinedAttachments,
    );

    this.logger.log(
      `[EmailService] - Sent activation email in language '${language}' to: ${email}`,
    );
  }

  /**
   * Send activation SMS to customer with their activation code
   */
  private async sendActivationSms(
    customerName: string,
    activationCode: string,
    shopifyOrder: ShopifyOrder,
  ): Promise<void> {
    // Extract phone number from order
    const phoneNumber = this.smsService.extractPhoneFromOrder(
      shopifyOrder.billing_address?.phone,
      shopifyOrder.shipping_address?.phone,
      shopifyOrder.billing_address?.country ||
        shopifyOrder.shipping_address?.country,
    );

    if (!phoneNumber) {
      this.logger.debug(
        `[SMS Debug] No valid phone number found for order ${
          shopifyOrder.order_number || shopifyOrder.id
        }`,
      );
      return; // No phone number available, skip SMS
    }

    // Detect language from billing address country
    const language =
      this.smsTemplateService.detectLanguageFromOrder(shopifyOrder);

    this.logger.debug(
      `[SMS Debug] Order billing country: ${shopifyOrder.billing_address?.country}`,
    );
    this.logger.debug(
      `[SMS Debug] Order shipping country: ${shopifyOrder.shipping_address?.country}`,
    );
    this.logger.debug(`[SMS Debug] Detected language: ${language}`);

    // Prepare template variables
    const templateVariables = {
      customerName,
      activationCode,
      orderNumber: shopifyOrder.name || '',
    };

    // Get localized SMS template
    const smsTemplate = this.smsTemplateService.getSmsTemplate(
      language,
      templateVariables,
    );

    // Send the SMS
    const messageSid = await this.smsService.sendSms(
      phoneNumber,
      smsTemplate.message,
    );

    this.logger.log(
      `[SmsService] - Sent activation SMS in language '${language}' to phone ending in ${phoneNumber.slice(
        -4,
      )}. Message SID: ${messageSid}`,
    );
  }

  /**
   * Determines if an email sending error should be retried
   */
  private isRetryableEmailError(error: any): boolean {
    if (!error) return false;

    const errorMessage = error.message?.toLowerCase() || '';
    const errorCode = error.code;

    // Network-related errors that should be retried
    const networkErrors = [
      'timeout',
      'network',
      'econnreset',
      'enotfound',
      'econnrefused',
      'etimedout',
      'socket hang up',
      'connection reset',
    ];

    // Mail service specific errors that should be retried
    const mailServiceErrors = [
      'mail sending failed',
      'temporary failure',
      'service unavailable',
      'rate limit',
      'too many requests',
    ];

    // Check error message
    const hasRetryableMessage = [...networkErrors, ...mailServiceErrors].some(
      (errorType) => errorMessage.includes(errorType),
    );

    // Check error codes
    const retryableCodes = [
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'EPIPE',
    ];
    const hasRetryableCode = retryableCodes.includes(errorCode);

    // Check error names
    const retryableNames = ['AbortError', 'TypeError', 'NetworkError'];
    const hasRetryableName = retryableNames.includes(error.name);

    return hasRetryableMessage || hasRetryableCode || hasRetryableName;
  }
}
