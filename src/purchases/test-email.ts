/**
 * Test script to verify email functionality
 * This script can be used to test the email sending functionality
 */

import { MailService } from '../mail/mail.service';
import { EmailTemplateService } from '../mail/email-template.service';

async function testEmailSending() {
  const mailService = new MailService();
  
  const testEmail = '<EMAIL>';
  const testName = '<PERSON>';
  const testActivationCode = 'AB3x';
  const testShopifyOrder = {
    id: '12345',
    order_number: '1001',
    total_price: '99.99',
    currency: 'USD',
  };

  const subject = 'Your Training Activation Code - IMVI';
  
  const htmlTemplate = `
  <!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Training Activation Code</title>
      <style>
          body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f4f4f4;
          }
          .container {
              background-color: #ffffff;
              padding: 30px;
              border-radius: 10px;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
              text-align: center;
              margin-bottom: 30px;
              padding-bottom: 20px;
              border-bottom: 2px solid #007bff;
          }
          .logo {
              font-size: 28px;
              font-weight: bold;
              color: #007bff;
              margin-bottom: 10px;
          }
          .activation-code {
              background: linear-gradient(135deg, #007bff, #0056b3);
              color: white;
              padding: 20px;
              border-radius: 8px;
              text-align: center;
              margin: 25px 0;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          .code {
              font-size: 32px;
              font-weight: bold;
              letter-spacing: 4px;
              margin: 10px 0;
              font-family: 'Courier New', monospace;
          }
      </style>
  </head>
  <body>
      <div class="container">
          <div class="header">
              <div class="logo">IMVI</div>
              <h1>Welcome to Training!</h1>
          </div>
          
          <p>Dear <strong>${testName}</strong>,</p>
          
          <p>Thank you for your purchase! We're excited to have you start your training journey with IMVI.</p>
          
          <div class="activation-code">
              <h2 style="margin: 0; font-size: 18px;">Your Activation Code</h2>
              <div class="code">${testActivationCode}</div>
              <p style="margin: 10px 0 0 0; font-size: 14px;">Use this code to activate your training</p>
          </div>
          
          <p><strong>Order Number:</strong> ${testShopifyOrder.order_number}</p>
          <p><strong>Order Total:</strong> ${testShopifyOrder.currency} ${testShopifyOrder.total_price}</p>
          
          <p>Best regards,<br>The IMVI Team</p>
      </div>
  </body>
  </html>
  `;

  const textTemplate = `
Dear ${testName},

Thank you for your purchase! We're excited to have you start your training journey with IMVI.

YOUR ACTIVATION CODE: ${testActivationCode}

Order Details:
- Order Number: ${testShopifyOrder.order_number}
- Order Total: ${testShopifyOrder.currency} ${testShopifyOrder.total_price}

Best regards,
The IMVI Team
  `;

  try {
    console.log('Testing email sending...');
    await mailService.sendMail(
      testEmail,
      testName,
      subject,
      textTemplate.trim(),
      htmlTemplate,
    );
    console.log('Email sent successfully!');
  } catch (error) {
    console.error('Failed to send email:', error);
  }
}

// Test logo injection specifically
async function testLogoInjection() {
  const emailTemplateService = new EmailTemplateService();

  const templateVariables = {
    customerName: 'Test User',
    activationCode: 'TEST123',
    orderNumber: 'TEST-001',
    currency: 'USD',
    totalPrice: '99.00',
    email: '<EMAIL>',
  };

  const template = emailTemplateService.getActivationEmailTemplate('en', templateVariables);

  console.log('=== PNG LOGO INJECTION TEST ===');
  console.log('HTML contains img tag:', template.html.includes('<img'));
  console.log('HTML contains cid:IMVILogo:', template.html.includes('cid:IMVILogo'));
  console.log('HTML contains logo-img class:', template.html.includes('logo-img'));
  console.log('HTML contains IMVI text fallback:', template.html.includes('<div class="logo">IMVI</div>'));
  console.log('Has inline attachments:', !!template.inlinedAttachments);
  if (template.inlinedAttachments) {
    console.log('Attachment ContentID:', template.inlinedAttachments[0].ContentID);
    console.log('Attachment ContentType:', template.inlinedAttachments[0].ContentType);
  }

  // Show a snippet around where the logo should be
  const logoIndex = template.html.indexOf('header');
  if (logoIndex !== -1) {
    const snippet = template.html.substring(logoIndex - 50, logoIndex + 200);
    console.log('Header section snippet:', snippet);
  }
}

// Uncomment the line below to run the test
// testEmailSending();

// Uncomment to test logo injection
// testLogoInjection();

export { testEmailSending, testLogoInjection };
