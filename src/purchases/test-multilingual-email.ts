/**
 * Test script to verify multilingual email functionality
 */

import { EmailTemplateService } from '../mail/email-template.service';
import { ShopifyOrder } from './interfaces/shopify.interface';

// Mock Shopify orders for different countries
const testOrders: { country: string; order: ShopifyOrder }[] = [
  {
    country: 'Sweden',
    order: {
      id: '12345',
      order_number: 'SE-1001',
      total_price: '999.00',
      currency: 'SEK',
      billing_address: {
        country: 'SE',
        city: 'Stockholm',
      },
      customer: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'Finland',
    order: {
      id: '12346',
      order_number: 'FI-1002',
      total_price: '89.90',
      currency: 'EUR',
      billing_address: {
        country: 'FI',
        city: 'Helsinki',
      },
      customer: {
        first_name: 'Aino',
        last_name: 'Virtanen',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'Norway',
    order: {
      id: '12347',
      order_number: 'NO-1003',
      total_price: '1200.00',
      currency: 'NOK',
      billing_address: {
        country: 'NO',
        city: 'Oslo',
      },
      customer: {
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'Germany',
    order: {
      id: '12348',
      order_number: 'DE-1004',
      total_price: '79.99',
      currency: 'EUR',
      billing_address: {
        country: 'DE',
        city: 'Berlin',
      },
      customer: {
        first_name: 'Hans',
        last_name: 'Mueller',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'Denmark',
    order: {
      id: '12349',
      order_number: 'DK-1005',
      total_price: '599.00',
      currency: 'DKK',
      billing_address: {
        country: 'DK',
        city: 'Copenhagen',
      },
      customer: {
        first_name: 'Mette',
        last_name: 'Nielsen',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'United States (fallback to English)',
    order: {
      id: '12350',
      order_number: 'US-1006',
      total_price: '99.99',
      currency: 'USD',
      billing_address: {
        country: 'US',
        city: 'New York',
      },
      customer: {
        first_name: 'John',
        last_name: 'Smith',
        email: '<EMAIL>',
      },
    },
  },
];

async function testMultilingualEmails() {
  console.log('🌍 Testing multilingual email templates...\n');

  const emailTemplateService = new EmailTemplateService();

  for (const { country, order } of testOrders) {
    console.log(`📧 Testing ${country}:`);
    
    try {
      // Detect language
      const language = emailTemplateService.detectLanguageFromOrder(order);
      console.log(`   Detected language: ${language}`);

      // Prepare template variables
      const templateVariables = {
        customerName: `${order.customer?.first_name} ${order.customer?.last_name}`,
        activationCode: 'AB3x',
        orderNumber: order.order_number || order.id?.toString() || '',
        currency: order.currency || 'USD',
        totalPrice: order.total_price || '0.00',
        email: order.customer?.email || '<EMAIL>',
      };

      // Get template
      const template = emailTemplateService.getActivationEmailTemplate(
        language,
        templateVariables,
      );

      console.log(`   Subject: ${template.subject}`);
      console.log(`   Template loaded successfully ✅`);
      
      // Show a snippet of the text template
      const textSnippet = template.text.substring(0, 100).replace(/\n/g, ' ');
      console.log(`   Text preview: ${textSnippet}...`);
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('🎉 Multilingual email test completed!');
}

// Test language detection specifically
function testLanguageDetection() {
  console.log('🔍 Testing language detection logic...\n');

  const emailTemplateService = new EmailTemplateService();
  
  const testCases = [
    { country: 'SE', expected: 'sv' },
    { country: 'Sweden', expected: 'sv' },
    { country: 'FI', expected: 'fi' },
    { country: 'Finland', expected: 'fi' },
    { country: 'NO', expected: 'no' },
    { country: 'Norway', expected: 'no' },
    { country: 'DE', expected: 'de' },
    { country: 'Germany', expected: 'de' },
    { country: 'DK', expected: 'da' },
    { country: 'Denmark', expected: 'da' },
    { country: 'US', expected: 'en' },
    { country: 'Unknown', expected: 'en' },
    { country: '', expected: 'en' },
  ];

  for (const { country, expected } of testCases) {
    const mockOrder: ShopifyOrder = {
      billing_address: { country },
    };
    
    const detected = emailTemplateService.detectLanguageFromOrder(mockOrder);
    const status = detected === expected ? '✅' : '❌';
    
    console.log(`${status} Country: "${country}" → Language: "${detected}" (expected: "${expected}")`);
  }

  console.log('\n🎯 Language detection test completed!');
}

// Test the new single template system
async function testSingleTemplateSystem() {
  console.log('🔧 Testing new single template system...\n');

  const emailTemplateService = new EmailTemplateService();

  // Test all supported languages
  const languages = ['en', 'sv', 'fi', 'no', 'de', 'da'];

  for (const language of languages) {
    console.log(`📧 Testing language: ${language}`);

    try {
      const templateVariables = {
        customerName: 'Test User',
        activationCode: 'TEST123',
        orderNumber: 'ORDER-001',
        currency: 'USD',
        totalPrice: '99.99',
        email: '<EMAIL>',
      };

      const template = emailTemplateService.getActivationEmailTemplate(
        language,
        templateVariables,
      );

      console.log(`   ✅ Subject: ${template.subject}`);

      // Verify that the template contains translated content
      const hasTranslatedContent = template.html.includes('TEST123') &&
                                   template.text.includes('TEST123') &&
                                   template.html.includes('Test User') &&
                                   template.text.includes('Test User');

      if (hasTranslatedContent) {
        console.log(`   ✅ Template variables replaced correctly`);
      } else {
        console.log(`   ❌ Template variables not replaced properly`);
      }

      // Show a snippet of the HTML to verify language-specific content
      const htmlSnippet = template.html.substring(
        template.html.indexOf('<h1>'),
        template.html.indexOf('</h1>') + 5
      );
      console.log(`   📄 HTML snippet: ${htmlSnippet}`);

    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }

    console.log('');
  }

  console.log('🎉 Single template system test completed!');
}

// Test the fallback mechanism
async function testFallbackMechanism() {
  console.log('🔧 Testing fallback mechanism...\n');

  // This test verifies that the EmailTemplateService can use the require() fallback
  // when the normal file reading fails. Since we can't easily simulate file read failures
  // in this test environment, we'll just verify that the service initializes correctly
  // and that the fallback translations would be used if needed.

  const emailTemplateService = new EmailTemplateService();

  console.log('✅ EmailTemplateService initialized successfully');
  console.log('✅ This means either the main translations.json was loaded,');
  console.log('   or the require() fallback mechanism worked correctly');

  // Test that we can get templates for all languages
  const languages = emailTemplateService.getSupportedLanguages();
  console.log(`✅ Supported languages: ${languages.join(', ')}`);

  // Verify the fallback would work by checking the default language
  const defaultLang = emailTemplateService.getDefaultLanguage();
  console.log(`✅ Default language: ${defaultLang}`);

  console.log('\n🎉 Fallback mechanism test completed!');
  console.log('💡 The fallback uses require() to load translations.json directly,');
  console.log('   eliminating all hardcoded duplication except for absolute emergencies.');
}

// Uncomment to run tests
// testMultilingualEmails().catch(console.error);
// testLanguageDetection();
// testSingleTemplateSystem().catch(console.error);
// testFallbackMechanism().catch(console.error);

export { testMultilingualEmails, testLanguageDetection, testSingleTemplateSystem, testFallbackMechanism };
