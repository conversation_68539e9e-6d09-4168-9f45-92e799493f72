/**
 * Test script to verify SMS functionality without requiring Twilio credentials
 * This script tests phone formatting, validation, and templates
 */

import { SmsTemplateService } from '../sms/sms-template.service';

// Mock SmsService for testing without Twilio credentials
class MockSmsService {
  formatPhoneNumber(phoneNumber: string, countryCode?: string): string {
    if (!phoneNumber) {
      throw new Error('Phone number is required');
    }

    // Remove all non-digit characters except +
    let cleaned = phoneNumber.replace(/[^\d+]/g, '');

    // If already has country code (starts with +), validate and return
    if (cleaned.startsWith('+')) {
      if (this.isValidPhoneNumber(cleaned)) {
        return cleaned;
      } else {
        throw new Error(`Invalid phone number format: ${phoneNumber}`);
      }
    }

    // Add country code based on country
    const countryCodeMap: { [key: string]: string } = {
      'US': '+1',
      'CA': '+1',
      'SE': '+46',
      'FI': '+358',
      'NO': '+47',
      'DK': '+45',
      'DE': '+49',
      'GB': '+44',
      'FR': '+33',
      'ES': '+34',
      'IT': '+39',
      'NL': '+31',
      'BE': '+32',
      'AT': '+43',
      'CH': '+41',
      'AU': '+61',
      'NZ': '+64',
      'IE': '+353',
    };

    const prefix = countryCode ? countryCodeMap[countryCode.toUpperCase()] : '+1';
    
    // Remove leading 0 if present (common in European numbers)
    if (cleaned.startsWith('0')) {
      cleaned = cleaned.substring(1);
    }

    const formatted = `${prefix || '+1'}${cleaned}`;
    
    // Validate the formatted number
    if (!this.isValidPhoneNumber(formatted)) {
      throw new Error(`Invalid phone number format after formatting: ${phoneNumber} -> ${formatted}`);
    }

    return formatted;
  }

  isValidPhoneNumber(phoneNumber: string): boolean {
    if (!phoneNumber) return false;
    
    // Basic validation: should start with + and have 10-15 digits
    const phoneRegex = /^\+[1-9]\d{9,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  extractPhoneFromOrder(billingPhone?: string, shippingPhone?: string, country?: string): string | null {
    const phone = billingPhone || shippingPhone;
    
    if (!phone) {
      return null;
    }

    try {
      const formatted = this.formatPhoneNumber(phone, country);
      return formatted;
    } catch (error) {
      return null;
    }
  }
}

async function testSmsOffline() {
  const mockSmsService = new MockSmsService();
  const smsTemplateService = new SmsTemplateService();
  
  console.log('🧪 Testing SMS functionality (offline mode)...\n');

  // Test phone number formatting
  console.log('📱 Testing phone number formatting...');
  
  try {
    const formattedUS = mockSmsService.formatPhoneNumber('(*************', 'US');
    console.log(`✅ US formatting: (************* -> ${formattedUS}`);
    
    const formattedSE = mockSmsService.formatPhoneNumber('070-123 45 67', 'SE');
    console.log(`✅ SE formatting: 070-123 45 67 -> ${formattedSE}`);
    
    const formattedFI = mockSmsService.formatPhoneNumber('************', 'FI');
    console.log(`✅ FI formatting: ************ -> ${formattedFI}`);
    
    const formattedNO = mockSmsService.formatPhoneNumber('90 12 34 56', 'NO');
    console.log(`✅ NO formatting: 90 12 34 56 -> ${formattedNO}`);
    
    const formattedDE = mockSmsService.formatPhoneNumber('0151 12345678', 'DE');
    console.log(`✅ DE formatting: 0151 12345678 -> ${formattedDE}`);
  } catch (error) {
    console.error('❌ Phone formatting test failed:', error.message);
  }

  // Test phone number validation
  console.log('\n🔍 Testing phone number validation...');
  
  const validNumbers = ['+15551234567', '+46701234567', '+358401234567', '+4790123456', '+4915112345678'];
  const invalidNumbers = ['123', 'abc', '+1', '555-1234', '+123456789012345678'];
  
  validNumbers.forEach(number => {
    const isValid = mockSmsService.isValidPhoneNumber(number);
    console.log(`${isValid ? '✅' : '❌'} ${number} is ${isValid ? 'valid' : 'invalid'}`);
  });
  
  invalidNumbers.forEach(number => {
    const isValid = mockSmsService.isValidPhoneNumber(number);
    console.log(`${!isValid ? '✅' : '❌'} ${number} is ${isValid ? 'valid' : 'invalid'} (expected invalid)`);
  });

  // Test SMS templates for different languages
  console.log('\n🌍 Testing SMS templates...');
  
  const languages = ['en', 'sv', 'fi', 'no', 'de', 'da'];
  const templateVariables = {
    customerName: 'John Doe',
    activationCode: 'AB3x',
    orderNumber: '1001',
  };

  languages.forEach(language => {
    try {
      const template = smsTemplateService.getSmsTemplate(language, templateVariables);
      console.log(`✅ ${language.toUpperCase()}: ${template.message}`);
    } catch (error) {
      console.error(`❌ Failed to get ${language} template:`, error.message);
    }
  });

  // Test extracting phone from mock Shopify order
  console.log('\n🛒 Testing phone extraction from Shopify order...');
  
  const mockShopifyOrders = [
    {
      name: 'US Order',
      billing_address: { phone: '(*************', country: 'US' },
      shipping_address: { phone: '+46701234567', country: 'SE' }
    },
    {
      name: 'Swedish Order',
      billing_address: { phone: '070-123 45 67', country: 'SE' },
      shipping_address: null
    },
    {
      name: 'Finnish Order',
      billing_address: null,
      shipping_address: { phone: '************', country: 'FI' }
    },
    {
      name: 'No Phone Order',
      billing_address: { country: 'US' },
      shipping_address: { country: 'US' }
    }
  ];

  mockShopifyOrders.forEach(order => {
    try {
      const extractedPhone = mockSmsService.extractPhoneFromOrder(
        order.billing_address?.phone,
        order.shipping_address?.phone,
        order.billing_address?.country || order.shipping_address?.country
      );
      
      if (extractedPhone) {
        console.log(`✅ ${order.name}: Extracted phone -> ${extractedPhone}`);
      } else {
        console.log(`⚠️  ${order.name}: No valid phone number found`);
      }
    } catch (error) {
      console.error(`❌ ${order.name}: Phone extraction failed ->`, error.message);
    }
  });

  // Test language detection
  console.log('\n🗣️  Testing language detection...');
  
  const mockOrdersForLanguage = [
    { billing_address: { country: 'US' }, expected: 'en' },
    { billing_address: { country: 'SE' }, expected: 'sv' },
    { billing_address: { country: 'FI' }, expected: 'fi' },
    { billing_address: { country: 'NO' }, expected: 'no' },
    { billing_address: { country: 'DE' }, expected: 'de' },
    { billing_address: { country: 'DK' }, expected: 'da' },
    { billing_address: { country: 'FR' }, expected: 'en' }, // Should fallback to English
  ];

  mockOrdersForLanguage.forEach(order => {
    const detectedLanguage = smsTemplateService.detectLanguageFromOrder(order as any);
    const isCorrect = detectedLanguage === order.expected;
    console.log(`${isCorrect ? '✅' : '❌'} ${order.billing_address.country} -> ${detectedLanguage} (expected: ${order.expected})`);
  });

  console.log('\n🎉 SMS offline functionality test completed!');
  console.log('💡 To test actual SMS sending, configure Twilio credentials and run test-sms.ts');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testSmsOffline().catch(console.error);
}

export { testSmsOffline };
