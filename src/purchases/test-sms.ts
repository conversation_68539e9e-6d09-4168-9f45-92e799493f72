/**
 * Test script to verify SMS functionality
 * This script can be used to test the SMS sending functionality
 */

import { SmsService } from '../sms/sms.service';
import { SmsTemplateService } from '../sms/sms-template.service';

async function testSmsSending() {
  const smsService = new SmsService();
  const smsTemplateService = new SmsTemplateService();
  
  // Test phone number (replace with your actual test number)
  const testPhoneNumber = '+1234567890'; // Replace with your test number
  const testCustomerName = '<PERSON>';
  const testActivationCode = 'AB3x';
  const testOrderNumber = '1001';

  // Test phone number formatting
  console.log('Testing phone number formatting...');
  
  try {
    const formattedUS = smsService.formatPhoneNumber('(*************', 'US');
    console.log(`✅ US formatting: (************* -> ${formattedUS}`);
    
    const formattedSE = smsService.formatPhoneNumber('070-123 45 67', 'SE');
    console.log(`✅ SE formatting: 070-123 45 67 -> ${formattedSE}`);
    
    const formattedFI = smsService.formatPhoneNumber('************', 'FI');
    console.log(`✅ FI formatting: ************ -> ${formattedFI}`);
  } catch (error) {
    console.error('❌ Phone formatting test failed:', error.message);
  }

  // Test phone number validation
  console.log('\nTesting phone number validation...');
  
  const validNumbers = ['+15551234567', '+46701234567', '+358401234567'];
  const invalidNumbers = ['123', 'abc', '+1', '555-1234'];
  
  validNumbers.forEach(number => {
    const isValid = smsService.isValidPhoneNumber(number);
    console.log(`${isValid ? '✅' : '❌'} ${number} is ${isValid ? 'valid' : 'invalid'}`);
  });
  
  invalidNumbers.forEach(number => {
    const isValid = smsService.isValidPhoneNumber(number);
    console.log(`${!isValid ? '✅' : '❌'} ${number} is ${isValid ? 'valid' : 'invalid'} (expected invalid)`);
  });

  // Test SMS templates for different languages
  console.log('\nTesting SMS templates...');
  
  const languages = ['en', 'sv', 'fi', 'no', 'de', 'da'];
  const templateVariables = {
    customerName: testCustomerName,
    activationCode: testActivationCode,
    orderNumber: testOrderNumber,
  };

  languages.forEach(language => {
    try {
      const template = smsTemplateService.getSmsTemplate(language, templateVariables);
      console.log(`✅ ${language.toUpperCase()}: ${template.message.substring(0, 50)}...`);
    } catch (error) {
      console.error(`❌ Failed to get ${language} template:`, error.message);
    }
  });

  // Test extracting phone from mock Shopify order
  console.log('\nTesting phone extraction from Shopify order...');
  
  const mockShopifyOrder = {
    billing_address: {
      phone: '(*************',
      country: 'US'
    },
    shipping_address: {
      phone: '+46701234567',
      country: 'SE'
    }
  };

  try {
    const extractedPhone = smsService.extractPhoneFromOrder(
      mockShopifyOrder.billing_address.phone,
      mockShopifyOrder.shipping_address.phone,
      mockShopifyOrder.billing_address.country
    );
    console.log(`✅ Extracted phone: ${extractedPhone}`);
  } catch (error) {
    console.error('❌ Phone extraction failed:', error.message);
  }

  // Test actual SMS sending (only if test phone number is provided)
  if (testPhoneNumber !== '+1234567890') {
    console.log('\nTesting actual SMS sending...');
    
    try {
      const template = smsTemplateService.getSmsTemplate('en', templateVariables);
      console.log(`Sending SMS to ${testPhoneNumber}...`);
      console.log(`Message: ${template.message}`);
      
      const messageSid = await smsService.sendSms(testPhoneNumber, template.message);
      console.log(`✅ SMS sent successfully! Message SID: ${messageSid}`);
    } catch (error) {
      console.error('❌ Failed to send SMS:', error.message);
    }
  } else {
    console.log('\n⚠️  Skipping actual SMS sending - please update testPhoneNumber with your test number');
  }

  console.log('\n🎉 SMS functionality test completed!');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testSmsSending().catch(console.error);
}

export { testSmsSending };
