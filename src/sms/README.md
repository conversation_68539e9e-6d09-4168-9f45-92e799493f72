# SMS Service for IMVI

This module provides SMS functionality using Twilio to send activation codes to customers alongside email notifications.

## Features

- Send SMS activation codes in multiple languages (English, Swedish, Finnish, Norwegian, German, Danish)
- Automatic phone number formatting and validation
- Country-based language detection
- Integration with Shopify webhook processing
- Comprehensive error handling and logging

## Environment Variables

Add the following environment variables to your `.env` file:

```env
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
```

### Getting Twilio Credentials

1. Sign up for a Twilio account at https://www.twilio.com
2. Go to the Twilio Console Dashboard
3. Find your Account SID and Auth Token in the "Account Info" section
4. Purchase a phone number from Twilio Console > Phone Numbers > Manage > Buy a number
5. Use the purchased number as your `TWILIO_PHONE_NUMBER`

## Usage

### Automatic SMS Sending

SMS messages are automatically sent when processing Shopify webhooks if:
1. A valid phone number is found in the billing or shipping address
2. The phone number can be properly formatted for the customer's country
3. Twilio credentials are properly configured

### Manual SMS Testing

Use the test script to verify SMS functionality:

```bash
# Run the SMS test script
npx ts-node src/purchases/test-sms.ts
```

Make sure to update the `testPhoneNumber` variable in the test script with your actual phone number for testing.

## Phone Number Formatting

The service automatically formats phone numbers based on the customer's country:

- **US/Canada**: +1
- **Sweden**: +46
- **Finland**: +358
- **Norway**: +47
- **Denmark**: +45
- **Germany/Austria/Switzerland**: +49/+43/+41
- **UK**: +44
- **And more...**

## Supported Languages

SMS templates are available in:
- **English (en)**: Default language
- **Swedish (sv)**: For Sweden
- **Finnish (fi)**: For Finland
- **Norwegian (no)**: For Norway
- **German (de)**: For Germany, Austria, Switzerland
- **Danish (da)**: For Denmark

## Error Handling

- SMS sending failures are logged but don't prevent order processing
- Invalid phone numbers are gracefully handled
- Missing Twilio credentials will log errors but won't crash the application
- Retry logic is not implemented for SMS (unlike email) as SMS failures are typically immediate

## Logging

The service provides detailed logging:
- Phone number formatting and validation
- Language detection
- SMS sending success/failure
- Privacy-conscious phone number masking in logs

## Integration Points

### Purchases Controller
- `sendActivationSms()`: Sends SMS when processing Shopify webhooks
- Integrated alongside existing email functionality

### Services Used
- `SmsService`: Core SMS sending functionality
- `SmsTemplateService`: Multi-language template management

## Security Considerations

- Phone numbers are masked in logs for privacy
- Twilio credentials should be kept secure
- SMS content is limited to activation codes and basic order information
- No sensitive customer data is included in SMS messages

## Troubleshooting

### Common Issues

1. **SMS not sending**: Check Twilio credentials and phone number format
2. **Invalid phone number**: Ensure phone numbers include country codes
3. **Wrong language**: Verify country detection logic in billing/shipping addresses
4. **Twilio errors**: Check Twilio console for account status and balance

### Debug Logging

Enable debug logging to see detailed SMS processing:
- Phone number extraction and formatting
- Language detection
- Template generation
- Twilio API responses
