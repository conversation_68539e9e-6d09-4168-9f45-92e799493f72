import { Injectable, Logger } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import { ShopifyOrder } from '../purchases/interfaces/shopify.interface';

interface SmsTemplate {
  message: string;
}

interface SmsTemplateVariables {
  customerName: string;
  activationCode: string;
  orderNumber: string;
}

@Injectable()
export class SmsTemplateService {
  private readonly logger = new Logger(SmsTemplateService.name);
  private readonly templatesPath = join(
    process.cwd(),
    'src',
    'sms',
    'templates',
  );
  private translationsConfig: any;

  constructor() {
    this.loadTranslations();
  }

  /**
   * Load SMS translations configuration
   */
  private loadTranslations(): void {
    try {
      const translationsPath = join(this.templatesPath, 'translations.json');
      this.logger.debug(`Loading SMS translations from: ${translationsPath}`);

      const translationsData = readFileSync(translationsPath, 'utf8');
      this.translationsConfig = JSON.parse(translationsData);

      this.logger.log('SMS translations loaded successfully');
    } catch (error) {
      this.logger.error('Failed to load SMS translations config:', error);
      this.logger.log('Using default SMS translations as fallback');

      // Fallback translations
      this.translationsConfig = {
        supportedLanguages: ['en', 'sv', 'fi', 'no', 'de', 'da'],
        defaultLanguage: 'en',
        countryToLanguage: {
          US: 'en',
          CA: 'en',
          GB: 'en',
          AU: 'en',
          NZ: 'en',
          SE: 'sv',
          Sweden: 'sv',
          FI: 'fi',
          Finland: 'fi',
          NO: 'no',
          Norway: 'no',
          DE: 'de',
          Germany: 'de',
          AT: 'de',
          Austria: 'de',
          CH: 'de',
          Switzerland: 'de',
          DK: 'da',
          Denmark: 'da',
        },
        translations: {
          en: {
            message:
              'Hi {{customerName}}! Your IMVI training activation code is: {{activationCode}}. Order: {{orderNumber}}. Download the IMVI app and enter this code to start training!',
          },
          sv: {
            message:
              'Hej {{customerName}}! Din IMVI träningsaktiveringskod är: {{activationCode}}. Beställning: {{orderNumber}}. Ladda ner IMVI-appen och ange denna kod för att börja träna!',
          },
          fi: {
            message:
              'Hei {{customerName}}! IMVI-harjoittelun aktivointikoodisi on: {{activationCode}}. Tilaus: {{orderNumber}}. Lataa IMVI-sovellus ja syötä tämä koodi aloittaaksesi harjoittelun!',
          },
          no: {
            message:
              'Hei {{customerName}}! Din IMVI treningsaktiveringskode er: {{activationCode}}. Bestilling: {{orderNumber}}. Last ned IMVI-appen og skriv inn denne koden for å starte trening!',
          },
          de: {
            message:
              'Hallo {{customerName}}! Ihr IMVI-Trainingsaktivierungscode lautet: {{activationCode}}. Bestellung: {{orderNumber}}. Laden Sie die IMVI-App herunter und geben Sie diesen Code ein, um mit dem Training zu beginnen!',
          },
          da: {
            message:
              'Hej {{customerName}}! Din IMVI træningsaktiveringskode er: {{activationCode}}. Ordre: {{orderNumber}}. Download IMVI-appen og indtast denne kode for at starte træning!',
          },
        },
      };
    }
  }

  /**
   * Detect language from Shopify order
   * @param shopifyOrder - Shopify order data
   * @returns Language code
   */
  detectLanguageFromOrder(shopifyOrder: ShopifyOrder): string {
    const country =
      shopifyOrder.billing_address?.country ||
      shopifyOrder.shipping_address?.country;

    if (!country) {
      this.logger.warn('No country found in order, using default language');
      return this.translationsConfig.defaultLanguage;
    }

    this.logger.debug(
      `[SMS Language Detection] Raw country value: "${country}"`,
    );

    // Try exact match first
    let language = this.translationsConfig.countryToLanguage[country];
    this.logger.debug(
      `[SMS Language Detection] Exact match for "${country}": ${
        language || 'none'
      }`,
    );

    // If no exact match, try case-insensitive match
    if (!language) {
      const countryUpper = country.toUpperCase();
      language = this.translationsConfig.countryToLanguage[countryUpper];
      this.logger.debug(
        `[SMS Language Detection] Case-insensitive match for "${countryUpper}": ${
          language || 'none'
        }`,
      );
    }

    // If still no match, try lowercase
    if (!language) {
      const countryLower = country.toLowerCase();
      language = this.translationsConfig.countryToLanguage[countryLower];
      this.logger.debug(
        `[SMS Language Detection] Lowercase match for "${countryLower}": ${
          language || 'none'
        }`,
      );
    }

    const finalLanguage = language || this.translationsConfig.defaultLanguage;
    this.logger.debug(
      `[SMS Language Detection] Final language for country "${country}": ${finalLanguage}`,
    );

    return finalLanguage;
  }

  /**
   * Get SMS template for specific language
   * @param language - Language code
   * @param variables - Template variables
   * @returns SMS template
   */
  getSmsTemplate(
    language: string,
    variables: SmsTemplateVariables,
  ): SmsTemplate {
    this.logger.debug(`Getting SMS template for language: ${language}`);

    // Ensure language is supported
    if (!this.translationsConfig.supportedLanguages.includes(language)) {
      this.logger.warn(
        `Language '${language}' not supported for SMS, falling back to default`,
      );
      language = this.translationsConfig.defaultLanguage;
    }

    // Get translation for the requested language
    const translation =
      this.translationsConfig.translations[language] ||
      this.translationsConfig.translations[
        this.translationsConfig.defaultLanguage
      ];

    // Replace variables in the message template
    let message = translation.message;
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      message = message.replace(new RegExp(placeholder, 'g'), value || '');
    });

    return { message };
  }

  /**
   * Get supported languages
   * @returns Array of supported language codes
   */
  getSupportedLanguages(): string[] {
    return this.translationsConfig.supportedLanguages;
  }

  /**
   * Get default language
   * @returns Default language code
   */
  getDefaultLanguage(): string {
    return this.translationsConfig.defaultLanguage;
  }
}
