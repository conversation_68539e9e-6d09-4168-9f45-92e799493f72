import { Injectable, Logger } from '@nestjs/common';
import { Twi<PERSON> } from 'twilio';

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);
  private twilioClient: Twilio;
  private fromPhoneNumber: string;

  constructor() {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    this.fromPhoneNumber = process.env.TWILIO_PHONE_NUMBER || '';

    if (!accountSid || !authToken) {
      this.logger.error(
        'Twilio credentials not found in environment variables',
      );
      throw new Error('Twilio credentials not configured');
    }

    if (!this.fromPhoneNumber) {
      this.logger.error(
        'Twilio phone number not found in environment variables',
      );
      throw new Error('Twilio phone number not configured');
    }

    this.twilioClient = new Twilio(accountSid, authToken);
    this.logger.log('SMS Service initialized with <PERSON>wi<PERSON>');
  }

  /**
   * Send SMS message to a phone number
   * @param to - Recipient phone number (should include country code)
   * @param message - SMS message content
   * @returns Promise<string> - Message SID from Twilio
   */
  async sendSms(to: string, message: string): Promise<string> {
    try {
      this.logger.log(`Sending SMS to ${this.maskPhoneNumber(to)}`);

      const result = await this.twilioClient.messages.create({
        body: message,
        from: this.fromPhoneNumber,
        to: to,
      });

      this.logger.log(`SMS sent successfully. Message SID: ${result.sid}`);
      return result.sid;
    } catch (error) {
      this.logger.error(
        `Failed to send SMS to ${this.maskPhoneNumber(to)}:`,
        error,
      );
      throw new Error(`SMS sending failed: ${error.message}`);
    }
  }

  /**
   * Validate and format phone number
   * @param phoneNumber - Raw phone number
   * @param countryCode - ISO country code (e.g., 'US', 'SE', 'FI')
   * @returns Formatted phone number with country code
   */
  formatPhoneNumber(phoneNumber: string, countryCode?: string): string {
    if (!phoneNumber) {
      throw new Error('Phone number is required');
    }

    // Remove all non-digit characters except +
    let cleaned = phoneNumber.replace(/[^\d+]/g, '');

    // If already has country code (starts with +), validate and return
    if (cleaned.startsWith('+')) {
      if (this.isValidPhoneNumber(cleaned)) {
        return cleaned;
      } else {
        throw new Error(`Invalid phone number format: ${phoneNumber}`);
      }
    }

    // Add country code based on country
    const countryCodeMap: { [key: string]: string } = {
      US: '+1',
      CA: '+1',
      SE: '+46',
      FI: '+358',
      NO: '+47',
      DK: '+45',
      DE: '+49',
      GB: '+44',
      FR: '+33',
      ES: '+34',
      IT: '+39',
      NL: '+31',
      BE: '+32',
      AT: '+43',
      CH: '+41',
      AU: '+61',
      NZ: '+64',
      IE: '+353',
    };

    const prefix = countryCode
      ? countryCodeMap[countryCode.toUpperCase()]
      : '+1';

    if (!prefix) {
      this.logger.warn(
        `Unknown country code: ${countryCode}, using default +1`,
      );
    }

    // Remove leading 0 if present (common in European numbers)
    if (cleaned.startsWith('0')) {
      cleaned = cleaned.substring(1);
    }

    const formatted = `${prefix || '+1'}${cleaned}`;

    // Validate the formatted number
    if (!this.isValidPhoneNumber(formatted)) {
      throw new Error(
        `Invalid phone number format after formatting: ${phoneNumber} -> ${formatted}`,
      );
    }

    return formatted;
  }

  /**
   * Validate if phone number is in correct format
   * @param phoneNumber - Phone number to validate
   * @returns boolean
   */
  isValidPhoneNumber(phoneNumber: string): boolean {
    if (!phoneNumber) return false;

    // Basic validation: should start with + and have 10-15 digits
    const phoneRegex = /^\+[1-9]\d{9,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Extract and validate phone number from Shopify order
   * @param billingPhone - Phone from billing address
   * @param shippingPhone - Phone from shipping address
   * @param country - Country code for formatting
   * @returns Formatted phone number or null if invalid
   */
  extractPhoneFromOrder(
    billingPhone?: string,
    shippingPhone?: string,
    country?: string,
  ): string | null {
    const phone = billingPhone || shippingPhone;

    if (!phone) {
      this.logger.debug('No phone number found in order');
      return null;
    }

    try {
      const formatted = this.formatPhoneNumber(phone, country);
      this.logger.debug(`Successfully formatted phone number for SMS`);
      return formatted;
    } catch (error) {
      this.logger.warn(`Failed to format phone number: ${error.message}`);
      return null;
    }
  }

  /**
   * Mask phone number for logging (privacy)
   * @param phoneNumber - Phone number to mask
   * @returns Masked phone number
   */
  private maskPhoneNumber(phoneNumber: string): string {
    if (!phoneNumber || phoneNumber.length < 4) return '***';

    const start = phoneNumber.substring(0, 3);
    const end = phoneNumber.substring(phoneNumber.length - 2);
    const middle = '*'.repeat(phoneNumber.length - 5);

    return `${start}${middle}${end}`;
  }
}
